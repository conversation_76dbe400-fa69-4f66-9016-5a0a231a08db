'use client'
import Collapse from '@/components/custom/collapse'
import HeroVideoDialog from '@/components/magicui/hero-video-dialog'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { durationToClockFormat, formatDuration } from '@/lib/utils'
import type { Course } from '@/types/course.type'
import { MonitorPlay } from 'lucide-react'
import { Fragment } from 'react'

function PreviewCourseDialog({ course }: { course: Course }) {
  return (
    <Dialog open={true} onOpenChange={}>
      <DialogContent className="sm:max-w-[1085px]">
        <DialogHeader className="p-1">
          <DialogTitle>Course Preview</DialogTitle>
        </DialogHeader>
        <Separator />
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-9">
            <div>
              {/* <HeroVideoDialog
                className="block dark:hidden shadow-none"
                animationStyle="top-in-bottom-out"
                videoSrc={course.promotion_video_url}
                thumbnailSrc={course.image_url}
                thumbnailAlt="Hero Video"
              /> */}
            </div>
            <h3 className="font-bold text-lg">Complete JavaScript Mastery</h3>
            <p className="font-thin">Learn modern JavaScript from basics to advanced</p>
            <div className="space-y-2 mt-4">
              <h4 className="font-semibold">Course content</h4>
              <div>
                {course.chapters?.map((chapter) => (
                  <Fragment key={chapter.id}>
                    <Collapse title={chapter.title}>
                      <ul className="flex flex-col">
                        {chapter.lessons?.map((lesson) => (
                          <li
                            key={lesson.id}
                            className="flex gap-4 items-center px-4 py-2 hover:bg-accent text-xs md:text-sm"
                          >
                            <MonitorPlay className="text-primary" size={20} />
                            <span>{lesson.title}</span>
                            <div className="ml-auto text-gray-400">
                              {durationToClockFormat(lesson.duration)}
                            </div>
                          </li>
                        ))}
                      </ul>
                    </Collapse>
                  </Fragment>
                ))}
              </div>
            </div>
          </div>
          <div className="col-span-3">
            <Card className="p-4 shadow-none bg-gray-50">
              <div className="font-semibold">Summary Course</div>
              <ul className="flex flex-col gap-2 font-thin">
                <li className="flex justify-between">
                  <span>Duration</span>
                  <span className="font-semibold"> {formatDuration(10000)}</span>
                </li>
                <li className="flex justify-between">
                  <span>Price</span>
                  <span className="font-semibold"> 10$</span>
                </li>
                <li className="flex justify-between">
                  <span>Lessons</span>
                  <span className="font-semibold"> 45 lessons</span>
                </li>
              </ul>
            </Card>
            <Card className="p-4 shadow-none bg-gray-50 mt-4">
              <div className="font-semibold">Instructor</div>
              <div className="flex items-center gap-2">
                <Avatar>
                  <AvatarFallback>CN</AvatarFallback>
                </Avatar>
                <div className="flex flex-col">
                  <div className="font-semibold text-sm">Erik</div>
                  <div className="text-xs"><EMAIL></div>
                </div>
              </div>
            </Card>
            <div className="flex flex-col gap-3 mt-5">
              <Button>Approve</Button>
              <Button variant="destructive">Reject</Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default PreviewCourseDialog
